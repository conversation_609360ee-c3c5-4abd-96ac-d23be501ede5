<template>
  <div class="image-carousel-container">
    <!-- 左箭头 -->
    <button
      class="carousel-arrow carousel-arrow-left"
      @click="scrollLeft"
      :disabled="isAtStart"
      :class="{ 'opacity-50 cursor-not-allowed': isAtStart }"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        ></path>
      </svg>
    </button>

    <!-- 图片容器 -->
    <div
      ref="scrollContainer"
      class="carousel-scroll-container"
      @scroll="handleScroll"
    >
      <div class="carousel-images-wrapper">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="carousel-image-item"
          :class="{ selected: selectedIndex === index }"
          @click="selectImage(index)"
        >
          <img
            :src="image.src"
            :alt="image.alt || `Image ${index + 1}`"
            class="carousel-image"
            @error="handleImageError"
          />
          <!-- 选中状态指示器 -->
          <!-- <div v-if="selectedIndex === index" class="selection-indicator">
            <svg
              class="w-4 h-4 text-blue-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 右箭头 -->
    <button
      class="carousel-arrow carousel-arrow-right"
      @click="scrollRight"
      :disabled="isAtEnd"
      :class="{ 'opacity-50 cursor-not-allowed': isAtEnd }"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        ></path>
      </svg>
    </button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from "vue";

// Props
const props = defineProps({
  images: {
    type: Array,
    required: true,
    default: () => [],
  },
  itemWidth: {
    type: Number,
    default: 80,
  },
  gap: {
    type: Number,
    default: 12,
  },
  scrollStep: {
    type: Number,
    default: 3,
  },
});

// Emits
const emit = defineEmits(["select", "scroll"]);

// Refs
const scrollContainer = ref(null);
const selectedIndex = ref(0);
const isAtStart = ref(true);
const isAtEnd = ref(false);

// 计算属性
const itemTotalWidth = computed(() => props.itemWidth + props.gap);

// 方法
const handleScroll = () => {
  if (!scrollContainer.value) return;

  const container = scrollContainer.value;
  isAtStart.value = container.scrollLeft <= 0;
  isAtEnd.value =
    container.scrollLeft >= container.scrollWidth - container.clientWidth - 1;

  emit("scroll", {
    scrollLeft: container.scrollLeft,
    isAtStart: isAtStart.value,
    isAtEnd: isAtEnd.value,
  });
};

const scrollLeft = () => {
  if (!scrollContainer.value || isAtStart.value) return;

  const scrollAmount = itemTotalWidth.value * props.scrollStep;
  scrollContainer.value.scrollBy({
    left: -scrollAmount,
    behavior: "smooth",
  });
};

const scrollRight = () => {
  if (!scrollContainer.value || isAtEnd.value) return;

  const scrollAmount = itemTotalWidth.value * props.scrollStep;
  scrollContainer.value.scrollBy({
    left: scrollAmount,
    behavior: "smooth",
  });
};

const selectImage = (index) => {
  selectedIndex.value = index;
  emit("select", {
    index,
    image: props.images[index],
  });

  // 滚动到选中的图片
  scrollToImage(index);
};

const scrollToImage = (index) => {
  if (!scrollContainer.value) return;

  const container = scrollContainer.value;
  const targetScrollLeft = index * itemTotalWidth.value;
  const containerWidth = container.clientWidth;
  const imagePosition = targetScrollLeft + props.itemWidth / 2;

  // 如果图片不在可视区域内，则滚动到合适位置
  if (
    imagePosition < container.scrollLeft + containerWidth / 4 ||
    imagePosition > container.scrollLeft + (containerWidth * 3) / 4
  ) {
    container.scrollTo({
      left: Math.max(
        0,
        targetScrollLeft - containerWidth / 2 + props.itemWidth / 2
      ),
      behavior: "smooth",
    });
  }
};

const handleImageError = (event) => {
  // 图片加载失败时的处理
  event.target.src =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkw0MCA0OEw1NiAzMlY1Nkg0MEgyNFYzMloiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+";
};

// 生命周期
onMounted(() => {
  nextTick(() => {
    handleScroll();
  });
});

// 暴露方法给父组件
defineExpose({
  scrollToImage,
  selectImage,
  scrollLeft,
  scrollRight,
});
</script>

<style scoped>
.image-carousel-container {
  @apply relative flex items-center bg-gray-50 rounded-lg p-2;
  min-height: 100px;
  width: 600px;
}

.carousel-arrow {
  @apply flex items-center justify-center w-8 h-8 bg-white rounded-full shadow-md hover:shadow-lg transition-all duration-200 z-10;
  @apply text-gray-600 hover:text-gray-800 border border-gray-200;
}

.carousel-arrow:hover:not(:disabled) {
  @apply bg-gray-50 transform scale-105;
}

.carousel-arrow:disabled {
  @apply cursor-not-allowed;
}

.carousel-arrow-left {
  @apply mr-2;
}

.carousel-arrow-right {
  @apply ml-2;
}

.carousel-scroll-container {
  @apply overflow-x-auto;
  width: calc(100% - 80px);
}

.carousel-scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.carousel-images-wrapper {
  @apply flex items-center;
  gap: 12px;
  padding: 4px 0;
}

.carousel-image-item {
  @apply relative flex-shrink-0 cursor-pointer transition-all duration-200;
  @apply hover:transform hover:scale-105 rounded-lg overflow-hidden;
  width: v-bind(itemWidth + "px");
  height: v-bind(itemWidth + "px");
}

.carousel-image-item.selected {
  box-shadow: 0px 6px 12px #919191;
}

.carousel-image {
  @apply w-full h-full object-cover rounded-lg;
}

.selection-indicator {
  @apply absolute top-1 right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md;
}

/* 自定义滚动条样式（可选） */
.carousel-scroll-container.show-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.carousel-scroll-container.show-scrollbar::-webkit-scrollbar {
  display: block;
  height: 4px;
}

.carousel-scroll-container.show-scrollbar::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 2px;
}

.carousel-scroll-container.show-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.carousel-scroll-container.show-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
</style>
