<template>
  <a-table
    :columns="columns"
    :data="data"
    :pagination="false"
    :bordered="false"
    :class="className"
    :scroll="{ y: 'auto', maxHeight: '380px' }"
  >
    <template #TYPE="{ record }">
      <a-tooltip :content="record.TYPE">
        <div class="flex items-center text-left">
          <BaseIcon icon="a-NFToffer" class="text-[24px] mr-[8px]" />
          <div class="floor overflow-one-hidden">
            {{ record.TYPE }}
          </div>
        </div>
      </a-tooltip>
    </template>
    <template #PRICE="{ record }">
      <div class="text-left">
        {{ record.PRICE }} <span class="text-[#595959]">ETH</span>
      </div>
    </template>
  </a-table>
</template>
<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import { useI18n } from "vue-i18n";
import BaseIcon from "@/components/BaseIcon/index.vue";
const { t } = useI18n();
defineProps({
  className: String,
});
const columns = [
  {
    title: t("views.nfts.details.collection.TYPE"),
    dataIndex: "TYPE",
    slotName: "TYPE",
    tooltip: true,
  },
  {
    title: t("views.nfts.details.collection.PRICE"),
    dataIndex: "PRICE",
    slotName: "PRICE",
  },
  {
    title: t("views.nfts.details.collection.QTY"),
    dataIndex: "QTY",
  },
  {
    title: t("views.nfts.details.collection.FROM"),
    dataIndex: "FROM",
    align: "right",
    ellipsis: true,
    tooltip: true,
  },
  {
    title: t("views.nfts.details.collection.EXPRIRY"),
    dataIndex: "EXPRIRY",
    align: "right",
  },
];
const data = reactive(
  new Array(50).fill({
    TYPE: t("views.nfts.details.collection.NFT_offer"),
    PRICE: 13.99,
    QTY: 1,
    FROM: t("views.nfts.details.collection.hattori_hanzooo"),
    EXPRIRY: "20h",
  })
);
</script>
