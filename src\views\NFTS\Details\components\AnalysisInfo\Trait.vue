<template>
    <a-table :columns="columns" :data="data" :pagination="false" :bordered="false">
        <template #COUNT="{ rowIndex }">
            <div :class="['count-col', data[rowIndex].COUNTStatus]">
                <div class="count">{{ data[rowIndex].COUNT }}</div>
                <div class="count-unit">7%</div>
            </div>
        </template>
        <template #FLOOR="{ rowIndex }">
            <div class="floor-col">
                <div class="floor">{{ data[rowIndex].FLOOR }}</div>
                <div class="floor-unit">{{ t('views.nfts.details.collection.eht') }}</div>
            </div>
        </template>
    </a-table>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const columns = [
    {
        title: t('views.nfts.details.collection.attribute'),
        dataIndex: 'ATTRIBUTE',
    },
    {
        title: t('views.nfts.details.collection.trait'),
        dataIndex: 'TRAIT',
    },
    {
        title: t('views.nfts.details.collection.count')+'(%)',
        dataIndex: 'COUNT',
        slotName: 'COUNT'
    },
    {
        title: t('views.nfts.details.collection.floor'),
        dataIndex: 'FLOOR',
        slotName: 'FLOOR'
    },
];

const data = reactive([
    {
        ATTRIBUTE: t('views.nfts.details.collection.face'),
        TRAIT: t('views.nfts.details.collection.default'),
        COUNT: 709,
        FLOOR: 13.99,
        COUNTStatus: 'color0'
    },
    {
        ATTRIBUTE: t('views.nfts.details.collection.hair'),
        TRAIT: t('views.nfts.details.collection.blueBrushcut'),
        COUNT: 709,
        FLOOR: 13.99,
        COUNTStatus: 'color1'
    },
    {
        ATTRIBUTE: t('views.nfts.details.collection.body'),
        TRAIT: t('views.nfts.details.collection.combo')  + t('views.nfts.details.collection.puffer'),
        COUNT: 709,
        FLOOR: 13.99,
        COUNTStatus: 'color2'
    },
    {
        ATTRIBUTE: t('views.nfts.details.collection.background'),
        TRAIT: t('views.nfts.details.collection.lightBlue'),
        COUNT: 709,
        FLOOR: 13.99,
        COUNTStatus: 'color3'
    },
    {
        ATTRIBUTE: t('views.nfts.details.collection.head'),
        TRAIT: t('views.nfts.details.collection.pink'),
        COUNT: 709,
        FLOOR: 13.99,
        COUNTStatus: 'color0'
    }
]);

</script>
<style lang="scss" scoped>
.floor-col {
    display: flex;
    align-items: center;
    gap: 4px;

    .floor {
        color: #000;
        font-size: 14px;
        font-weight: 700;
    }

    .floor-unit {
        color: #595959;
        font-size: 14px;
        font-weight: 400;
    }
}

.count-col {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 0 8px;
    border-radius: 4px;
    background: #EFF5FF;

    .count {
        color: #000;
        font-size: 14px;
        font-weight: 400;
    }

    .count-unit {
        color: #416EEA;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
    }

}

.count-col.color0 {
    background: #EFF5FF;

    .count-unit {
        color: #416EEA;
    }
}

.count-col.color1 {
    background: #E8FFF0;

    .count-unit {
        color: #00D46A;
    }
}

.count-col.color2 {
    background: #FFFBEB;

    .count-unit {
        color: #F59E0B;
    }
}

.count-col.color3 {
    background: #F7E6FF;

    .count-unit {
        color: #AF52DE;
    }
}
</style>
