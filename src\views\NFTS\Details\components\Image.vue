<template>
  <div class="relative max-w-[100%]" :class="className">
    <img :class="classList" :src="url" @click="handleImag" />
    <EnlargeIcon
      v-if="enlarge"
      className="absolute top-[12px] right-[12px] w-[40px] h-[40px] rounded-[7px]"
      @click="dialogVisible = true"
    />
  </div>
  <a-modal
    v-model:visible="dialogVisible"
    class="ntfs-image-dialog"
    modal-class="ntfs-image-modal"
    top="0"
    :hide-title="true"
    :closable="false"
    :footer="false"
  >
    <div class="zoom-img-content flex items-center justify-center">
      <img
        :src="url"
        alt=""
        class="max-w-[80vw] max-h-[80vh] object-cover rounded-[12px]"
      />
    </div>
    <EnlargeIcon
      @click="dialogVisible = false"
      className="absolute top-[32px] right-[32px] w-[40px] h-[40px] rounded-[7px]"
    />
  </a-modal>
</template>
<script setup>
import { computed } from "vue";
import EnlargeIcon from "@/views/NFTS/Details/components/enlargeIcon.vue";
import imgUrl from "@/assets/NFTS/details/detail_1.png";

const props = defineProps({
  url: {
    type: String,
    default: imgUrl,
  },
  className: {
    type: String,
  },
  imgList: {
    type: Array,
    default: () => [],
  },
  enlarge: {
    type: Boolean,
    default: false,
  },
});
const classList = computed(
  () =>
    props.className +
    (props.enlarge ? " cursor-pointer " : " ") +
    " object-cover"
);
const dialogVisible = ref(false);
function handleImag() {
  if (!props.enlarge) return;
  dialogVisible.value = true;
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.ntfs-image-modal {
  width: 100%;
  height: 100%;
  background: transparent;
}

.ntfs-image-dialog {
  background: transparent;
  box-shadow: 0 0 0 transparent;
  width: 100%;
  height: 100%;
  padding-top: 0;
  padding-bottom: 0;
  .arco-modal-body {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
