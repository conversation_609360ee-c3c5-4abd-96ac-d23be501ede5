<template>
  <div id="common-nfts-detail-content">
    <div class="details-box">
      <div class="basic-box">
        <div class="left-box w-[656px]">
          <div>
            <Image
              :url="imgUrl"
              :enlarge="true"
              className="w-[576px] h-[576px] rounded-[16px] block mx-auto"
            />
          </div>
          <AnalysisInfo class="mt-[37px]" pageType="collection" />
        </div>
        <div class="right-box w-[656px]">
          <BasicInfo :showNameIconGroups="false" :showSubInfo="true" />
        </div>
      </div>

      <div class="more-box">
        <MoreCollection />
      </div>
    </div>
  </div>
</template>

<script setup>
import Image from "@/views/NFTS/Details/components/Image.vue";
import imgUrl from "@/assets/NFTS/details/detail_1.png";
import AnalysisInfo from "../components/AnalysisInfo/index.vue";
import BasicInfo from "../components/BasicInfo/index.vue";
import MoreCollection from "../components/MoreCollection/index.vue";
import { useRouter } from "vue-router";
import bus from "@/utils/eventBus";

const props = defineProps({
  isFromMoadl: Boolean,
});

const router = useRouter();

const changeType = () => {
  router.push("/NFTSCollectionAnalysisDetail");
};

onMounted(() => {
  if (!props.isFromMoadl) {
    bus.on("NFTSDetailsIsAnalysis", changeType);
  }
});
onBeforeUnmount(() => {
  bus.off("NFTSDetailsIsAnalysis", changeType);
});
</script>
<style lang="scss" scoped></style>
