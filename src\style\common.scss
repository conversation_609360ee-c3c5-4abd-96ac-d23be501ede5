#common-nfts-detail-content {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  width: 100%;
  &.analysis-detials-box{
    display:block;
    width: 1440px;
  }
  .basic-box {
    display: grid;
    grid-template-columns: auto auto;
    gap: 64px;
  }

  .more-box {
    margin-top: 48px;
    width: 100%;
    overflow-x: auto;
    .items-box {
      width: 100%;
      overflow-x: auto;
    }
  }
  .overflow-one-hidden {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .info-outline-button {
    border-color: #e6e6e6;
    color: #000000;
    padding: 0 12px;
  }
  .arco-btn-info {
    background-color: #f2f2f2;
  }
  .arco-table-td {
    border-bottom-width: 0px;
  }
  .arco-table-header {
    background-color: transparent;
  }
  .arco-table-th {
    background-color: transparent;
    border-bottom-width: 1px;
    color: rgba(89, 89, 89, 0.65);
    font-size: 12px;
    line-height: 140%;
  }

  .arco-radio-group-button {
    border-radius: 16px;
    .arco-radio-checked {
      border-radius: 20px;
      color: #000;
      font-weight: 600;
    }
    .arco-radio-button:hover {
      border-radius: 20px;
    }
  }
  .coin-tabs {
    .arco-descriptions-item-label {
      color: #8c8c8c;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      padding-right: 60px;
      padding-bottom: 3px;
      &:last-child {
        padding-right: 0;
      }
    }
    .arco-descriptions-item-label-inline {
      margin-bottom: 0;
    }
    .arco-descriptions-item-value {
      color: #000;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      padding-right: 60px;
      &:last-child {
        padding-right: 0;
      }
    }
  }
  .common-card-border {
    .arco-card-body {
      padding: 24px;
    }
  }
  .activity-common-select {
    border-radius: 8px;
    &.w-240px {
      width: 240px;
    }
    .arco-tag {
      border-radius: 20px;
    }
  }
  .arco-tabs-tab {
    &:first-child {
      margin-left: 0;
    }
    .arco-tabs-tab-title {
      font-size: 14px;
      color: #8c8c8c;
    }
    &.arco-tabs-tab-active {
      .arco-tabs-tab-title {
        color: #000000;
        font-weight: 500;
      }
    }
  }
}

.cus-modal {
  .arco-modal-body {
    padding: 0px;
    overflow-y: auto;
    max-height: 836px;
    .dialog-header {
      display: flex;
      height: 72px;
      padding: 16px 12px 16px 24px;
      justify-content: space-between;
      align-items: center;
      position: relative;
      border-bottom: 1px solid #f2f2f2;
      .close-icon {
        position: absolute;
        top: 26px;
        right: 12px;
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
    .content-box {
      padding: 24px 32px 16px 32px;
    }
  }
}
