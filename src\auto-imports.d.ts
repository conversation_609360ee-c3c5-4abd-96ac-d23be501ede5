/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const Leagues: typeof import('./api/screen.js')['Leagues']
  const Plans: typeof import('./api/screen.js')['Plans']
  const UserInfo: typeof import('./api/user.js')['UserInfo']
  const UserInfos: typeof import('./api/nuser.js')['UserInfos']
  const addSquareStrategy: typeof import('./api/square.js')['addSquareStrategy']
  const buyPlan: typeof import('./api/screen.js')['buyPlan']
  const checkRoles: typeof import('./stores/roles.js')['checkRoles']
  const checkUtype: typeof import('./stores/roles.js')['checkUtype']
  const computed: typeof import('vue')['computed']
  const connect: typeof import('./api/user.js')['connect']
  const connects: typeof import('./api/nuser.js')['connects']
  const copyPlan: typeof import('./api/screen.js')['copyPlan']
  const createApp: typeof import('vue')['createApp']
  const createPolicy: typeof import('./api/edit.js')['createPolicy']
  const customRef: typeof import('vue')['customRef']
  const dataToolStore: typeof import('./stores/tool.js')['dataToolStore']
  const deactive: typeof import('./api/user.js')['deactive']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const deletePlan: typeof import('./api/screen.js')['deletePlan']
  const deletePolicy: typeof import('./api/edit.js')['deletePolicy']
  const deleteSquarePolicy: typeof import('./api/edit.js')['deleteSquarePolicy']
  const detailEarly: typeof import('./api/match.js')['detailEarly']
  const detailLive: typeof import('./api/match.js')['detailLive']
  const detailRecord: typeof import('./api/match.js')['detailRecord']
  const editPassword: typeof import('./api/myCenter.js')['editPassword']
  const editStrategyStore: typeof import('./stores/edit.js')['editStrategyStore']
  const editStrategyStore2: typeof import('./stores/edit copy.js')['editStrategyStore2']
  const effectScope: typeof import('vue')['effectScope']
  const favorAdd: typeof import('./api/match.js')['favorAdd']
  const favorDelete: typeof import('./api/match.js')['favorDelete']
  const favorEarly: typeof import('./api/match.js')['favorEarly']
  const favorLive: typeof import('./api/match.js')['favorLive']
  const favorRealy: typeof import('./api/match.js')['favorRealy']
  const favorUpLive: typeof import('./api/match.js')['favorUpLive']
  const favorUpRealy: typeof import('./api/match.js')['favorUpRealy']
  const filterEarly: typeof import('./api/match.js')['filterEarly']
  const filterRecord: typeof import('./api/match.js')['filterRecord']
  const ftInplay: typeof import('./api/screen.js')['ftInplay']
  const getAmidithion: typeof import('./api/match.js')['getAmidithion']
  const getAmidithionDetail: typeof import('./api/match.js')['getAmidithionDetail']
  const getAmidithionZSChart: typeof import('./api/match.js')['getAmidithionZSChart']
  const getAttention: typeof import('./api/match.js')['getAttention']
  const getBeforeOptionLS: typeof import('./api/edit.js')['getBeforeOptionLS']
  const getBeforeReTest: typeof import('./api/edit.js')['getBeforeReTest']
  const getBeforeReTestDate: typeof import('./api/edit.js')['getBeforeReTestDate']
  const getBeforeTable: typeof import('./api/edit.js')['getBeforeTable']
  const getBeforeTeamTable: typeof import('./api/edit.js')['getBeforeTeamTable']
  const getCourse: typeof import('./api/match.js')['getCourse']
  const getCourseDetail: typeof import('./api/match.js')['getCourseDetail']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDetailDate: typeof import('./api/match.js')['getDetailDate']
  const getIndexLine: typeof import('./api/edit.js')['getIndexLine']
  const getIndexOptionLS: typeof import('./api/edit.js')['getIndexOptionLS']
  const getIndexTable: typeof import('./api/edit.js')['getIndexTable']
  const getLeagueData: typeof import('./api/dataTools_inMatch.js')['getLeagueData']
  const getLiveOptionLS: typeof import('./api/edit.js')['getLiveOptionLS']
  const getLivePressureLine: typeof import('./api/edit.js')['getLivePressureLine']
  const getLiveTable: typeof import('./api/edit.js')['getLiveTable']
  const getMatch: typeof import('./api/dataTools_inMatch.js')['getMatch']
  const getMatchData: typeof import('./api/dataTools_inMatch.js')['getMatchData']
  const getMyPlans: typeof import('./api/dataTools_inMatch.js')['getMyPlans']
  const getMyPolicy: typeof import('./api/edit.js')['getMyPolicy']
  const getMyPolicyAll: typeof import('./api/edit.js')['getMyPolicyAll']
  const getPolicyCount: typeof import('./api/edit.js')['getPolicyCount']
  const getSetFilter: typeof import('./api/dataTools_inMatch.js')['getSetFilter']
  const getZSChart: typeof import('./api/match.js')['getZSChart']
  const h: typeof import('vue')['h']
  const historyLastTen: typeof import('./api/screen.js')['historyLastTen']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const list: typeof import('./api/match.js')['list']
  const listAllLive: typeof import('./api/match.js')['listAllLive']
  const listEarly: typeof import('./api/match.js')['listEarly']
  const listRecord: typeof import('./api/match.js')['listRecord']
  const listSquareStrategy: typeof import('./api/square.js')['listSquareStrategy']
  const listUpLive: typeof import('./api/match.js')['listUpLive']
  const listUserPay: typeof import('./api/user.js')['listUserPay']
  const list_all_live: typeof import('./api/match.js')['list_all_live']
  const login: typeof import('./api/user.js')['login']
  const logins: typeof import('./api/nuser.js')['logins']
  const markRaw: typeof import('vue')['markRaw']
  const matchDetail: typeof import('./api/match.js')['matchDetail']
  const matchPolicyHistory: typeof import('./api/edit.js')['matchPolicyHistory']
  const myPlans: typeof import('./api/screen.js')['myPlans']
  const myPlansNew: typeof import('./api/screen.js')['myPlansNew']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const register: typeof import('./api/user.js')['register']
  const registers: typeof import('./api/nuser.js')['registers']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setFilter: typeof import('./api/screen.js')['setFilter']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sharePolicy: typeof import('./api/edit.js')['sharePolicy']
  const stopPolicy: typeof import('./api/edit.js')['stopPolicy']
  const toAddTop: typeof import('./api/match.js')['toAddTop']
  const toDeleteTop: typeof import('./api/match.js')['toDeleteTop']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const toolIndexCharDate: typeof import('./api/tool.js')['toolIndexCharDate']
  const toolIndexCollect: typeof import('./api/tool.js')['toolIndexCollect']
  const toolIndexDate: typeof import('./api/tool.js')['toolIndexDate']
  const toolIndexGetPK: typeof import('./api/tool.js')['toolIndexGetPK']
  const toolIndexOptionLS: typeof import('./api/tool.js')['toolIndexOptionLS']
  const toolIndexOptionLS2: typeof import('./api/tool.js')['toolIndexOptionLS2']
  const toolIndexOptionPK: typeof import('./api/tool.js')['toolIndexOptionPK']
  const toolIndexOptionPKTab: typeof import('./api/tool.js')['toolIndexOptionPKTab']
  const toolIndexOptionZS: typeof import('./api/tool.js')['toolIndexOptionZS']
  const toolIndexSearch: typeof import('./api/tool.js')['toolIndexSearch']
  const toolIndexUncollect: typeof import('./api/tool.js')['toolIndexUncollect']
  const triggerRef: typeof import('vue')['triggerRef']
  const ucCheck: typeof import('./api/nuser.js')['ucCheck']
  const unref: typeof import('vue')['unref']
  const updatePolicy: typeof import('./api/edit.js')['updatePolicy']
  const updatePwd: typeof import('./api/user.js')['updatePwd']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDialog: typeof import('./hooks/dialog.js')['useDialog']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useModel: typeof import('vue')['useModel']
  const useNotificationStore: typeof import('./stores/notification.js')['useNotificationStore']
  const usePage: typeof import('./hooks/page.js')['usePage']
  const useRequest: typeof import('./hooks/request.js')['useRequest']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useUserStore: typeof import('./stores/user.js')['useUserStore']
  const userInfo: typeof import('./api/user.js')['userInfo']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
}
