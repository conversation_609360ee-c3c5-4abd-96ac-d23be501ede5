{"components.layout.ob523l": "Simplified Chinese", "components.layout.privacy-policy": "Privacy Policy", "components.layout.terms-of-service": "Terms of Service", "views": {"nfts": {"details": {"collection": {"ACTION": "ACTION", "Activity": "Activity", "BUY_FOR": "BUY FOR", "Batch_Buy": "Batch Buy", "Blockchain_details": "Blockchain details", "Bored Ape Yacht Club": "Bored Ape Yacht Club", "Buy": "Buy", "CHAIN": "CHAIN", "Collection_Details": "Collection Details", "Collection_Floor": "Collection Floor", "Contract_Address": "Contract Address", "EXPRIRY": "EXPRIRY", "FROM": "FROM", "Make_offer": "Make offer", "NFT_offer": "NFT offer", "Orders": "Orders", "PRICE": "PRICE", "Pudgy_PenguinsPudgy": "Pud<PERSON>udgy", "QTY": "QTY", "RARITY": "RARITY", "Sale_ends_in": "Sale ends in 29 days", "TOP_OFFER": "TOP OFFER", "TYPE": "TYPE", "Token_Standard": "Token Standard", "WETH": "WETH", "about": "About Doodle #9752", "about1": "About", "aboutDesc": "A community-driven collectibles  project featuring art by <PERSON><PERSON>. Doodles come in a joyful range of\n            colors, traits and sizes with acollection size of 10,000. Each Doodle allows its owner to vote for\n            experiences and activations paid for by the Doodles CommunityTreasury. <PERSON><PERSON> is the working allas for\n            <PERSON>, a Canadian-based illustrator, designer, animator and muralist.", "aboutDesc1": "5000 Doges Offering Solana On Chain Services - Web3 Software Development -\n      Blockchain Education", "abstract": "Abstract", "allTime": "All time", "analysis": "Analysis", "apeChain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "ART", "attribute": "ATTRIBUTE", "aug_12": "Aug 12", "aug_13": "Aug 13", "aug_7": "Aug 7", "background": "Background", "batchOffer": "Batch Make offer", "blueBrushcut": "blue brushcut", "body": "body", "buy_now": "Buy now", "by": "By:The Doge Capital", "combo": "combo", "count": "COUNT", "day": "day", "days": "days", "default": "default", "detailDialog": "Details pop-up", "doodle": "Doodle", "doodle_1": "Pud<PERSON>udgy", "eht": "ETH", "ethereum": "Ethereum", "face": "face", "filter": "Please select ...", "floor": "FLOOR", "hair": "Hair", "hattori_hanzooo": "hattori hanzooo", "head": "head", "independent": "Independent details", "info": "info", "jul_14": "Jul 14", "jul_26": "Jul 26", "lastSale": "LAST SALE", "lastUpdate": "LAST UPDATE", "lightBlue": "light blue", "more_from_this_collection": "More from this collection", "open": "open", "pink": "pink", "priceHistory": "Price history", "puffer": "puffer", "trait": "TRAIT", "view_collection": "View Collection"}}}}}