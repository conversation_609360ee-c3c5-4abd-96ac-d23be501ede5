<template>
  <div id="common-nfts-detail-content" class="analysis-detials-box">
    <div class="top-box flex">
      <Image :url="imgUrl" :enlarge="true" className="w-[256px] h-[256px] rounded-[16px] flex-shrink-0 mr-[32px]" />
      <div class="flex-grow">
        <InfoTop :showNameIconGroups="false" :showSubInfo="true" :showBatchButtons="false" />
        <div class="w-[320px] mt-[34px]">
          <BatchButtons className="flex gap-[16px] flex-col-reverse items-start"
            buttonClass="w-[320px] px-[0!important] h-[48px!important] font-[700!important]" />
        </div>
      </div>
    </div>
    <div class="bottom-box">
      <ComparativeAnalysis />
      <PriceHistory class="mt-[32px]" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import ComparativeAnalysis from "@/views/NFTS/Details/components/AnalysisInfo/ComparativeAnalysis.vue";
import PriceHistory from "@/views/NFTS/Details/components/AnalysisInfo/PriceHistory.vue";
import Image from "@/views/NFTS/Details/components/Image.vue";
import imgUrl from "@/assets/NFTS/details/detail_1.png";
import InfoTop from "@/views/NFTS/Details/components/BasicInfo/InfoTop/index.vue";
import BatchButtons from "@/views/NFTS/Details/components/BasicInfo/batchButtons.vue";

import { useRouter } from 'vue-router'
import bus from '@/utils/eventBus'

const router = useRouter()

const changeType = () => {
  router.push('/NFTSCollectionDetail')
}

onMounted(() => {
  bus.on('NFTSDetailsIsAnalysis', changeType)
})

onBeforeUnmount(() => {
  bus.off('NFTSDetailsIsAnalysis', changeType)
})

</script>
<style lang="scss" scoped>
.bottom-box {
  margin-top: 48px;
  width: 1440px;
}
</style>
