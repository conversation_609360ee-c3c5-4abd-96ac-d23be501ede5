<template>
  <div id="common-nfts-detail-content">
    <div class="details-box">
      <div class="basic-box">
        <div class="left-box w-[656px]">
          <div>
            <Image
              :url="curImgUrl"
              :enlarge="true"
              className="w-[576px] h-[576px] rounded-[16px] block mx-auto"
            />
          </div>
          <AnalysisInfo class="mt-[37px]" />
        </div>
        <div class="right-box w-[656px]">
          <BasicInfo :showNameIconGroups="true" :showSubInfo="false" />
        </div>
      </div>

      <!-- <div class="more-box border">
        <MoreCollection />
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import Image from "@/views/NFTS/Details/components/Image.vue";
import imgUrl from "@/assets/NFTS/details/detail_1.png";
import AnalysisInfo from "../components/AnalysisInfo/index.vue";
import BasicInfo from "../components/BasicInfo/index.vue";
// import MoreCollection from "../components/MoreCollection/index.vue";
import bus from '@/utils/eventBus'

defineProps({
  curImgUrl: {
    type: String,
    default: imgUrl,
  },
});

const router = useRouter()

const changeType = () => {
  console.log(222);
  
  router.push('/NFTSIndependentAnalysisDetail')
}

onMounted(() => {
  console.log('000');
  
  bus.on("NFTSDetailsIsAnalysis", changeType);
});

onBeforeUnmount(() => {
   console.log(111);
  bus.off('NFTSDetailsIsAnalysis', changeType)
})

</script>
<style lang="scss" scoped></style>
