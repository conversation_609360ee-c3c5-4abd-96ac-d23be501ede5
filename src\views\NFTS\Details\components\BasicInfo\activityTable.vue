<template>
  <div :class="className">
    <a-select
      v-model="selectVal"
      class="w-240px activity-common-select"
      :placeholder="$t('views.nfts.details.collection.filter')"
      multiple
      allow-clear
      :scrollbar="true"
      :max-tag-count="2"
    >
      <a-option
        v-for="item in Object.keys(salesList)"
        :key="item"
        :value="item"
      >
        {{ item }}
      </a-option>
    </a-select>
    <a-table
      class="mt-[28px]"
      :columns="columns"
      :data="data"
      :pagination="false"
      :bordered="false"
      :scroll="{ x: 'auto', y: 'auto', maxHeight: '380px' }"
    >
      <template #EVENT="{ record }">
        <a-tooltip :content="record.EVENT">
          <div class="flex items-center">
            <BaseIcon
              :icon="salesList[record.EVENT]"
              class="text-[24px] mr-[8px]"
            />
            <div class="floor overflow-one-hidden">
              {{ record.EVENT }}
            </div>
          </div>
        </a-tooltip>
      </template>
      <template #PRICE="{ record }">
        {{ record.PRICE }} <span class="text-[#595959]">ETH</span>
      </template>
    </a-table>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import { useI18n } from "vue-i18n";
import BaseIcon from "@/components/BaseIcon/index.vue";
const { t } = useI18n();
defineProps({
  className: String,
});
const salesList = {
  Sale: "shopping_cart",
  Listing: "tag",
  Transfer: "swap",
  Mint: "plus-circle",
  "NFT Offer": "a-NFToffer",
};
const selectVal = ref(["Sale", "Listing"]);
const columns = [
  {
    title: "EVENT",
    dataIndex: "EVENT",
    slotName: "EVENT",
    width: 170,
  },
  {
    title: "PRICE",
    dataIndex: "PRICE",
    slotName: "PRICE",
    align: "right",
  },

  {
    title: "FROM",
    dataIndex: "FROM",
  },
  {
    title: "TO",
    dataIndex: "TO",
    align: "right",
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "TIME",
    dataIndex: "TIME",
    align: "right",
  },
];
const defaultData = Object.keys(salesList).map((item) => ({
  EVENT: item,
  PRICE: 13.99,
  FROM: "bbb017",
  TO: "hattori hanzooo",
  TIME: "20h ago",
}));
let data = reactive(defaultData);
watch(
  selectVal,
  (val) => {
    if (!val || val.length === 0) {
      data = defaultData;
    } else {
      data = defaultData.filter((item) => val.includes(item.EVENT));
    }
  },
  { immediate: true }
);
</script>
