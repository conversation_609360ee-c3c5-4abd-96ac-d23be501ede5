{"name": "ntf-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "npm run dev"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.9.0", "axios": "^1.7.7", "echarts": "^5.6.0", "element-plus": "^2.8.3", "js-yaml": "^4.1.0", "mitt": "^3.0.1", "pinia": "^2.2.4", "sass": "^1.78.0", "tailwindcss": "^3.4.17", "unplugin-auto-import": "^0.18.3", "vue": "^3.4.37", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.4.5"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@vitejs/plugin-vue": "^5.1.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "vite": "^5.4.1"}}