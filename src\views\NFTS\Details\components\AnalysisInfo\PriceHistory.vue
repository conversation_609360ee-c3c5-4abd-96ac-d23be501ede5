<template>
  <div>
    <a-radio-group v-model="date" type="button">
      <a-radio value="0">{{
        $t("views.nfts.details.collection.allTime")
      }}</a-radio>
      <a-radio value="1"
        >1 {{ $t("views.nfts.details.collection.day") }}</a-radio
      >
      <a-radio value="7"
        >7 {{ $t("views.nfts.details.collection.days") }}</a-radio
      >
      <a-radio value="30"
        >30 {{ $t("views.nfts.details.collection.days") }}</a-radio
      >
    </a-radio-group>
    <BaseCharts v-model="chartsOptions" height="300px" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import BaseCharts from "@/components/BaseCharts/index.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const date = ref('0');

const getData = () => {
  return Array(11)
    .fill()
    .map((_, index) => (Math.random() * 0.06).toFixed(2));
};

const chartsData1 = ref(getData());
const chartsData2 = ref(getData());
const chartsData3 = ref(getData());

const colors = ["#0054FA", "#00DE73", "#627EEA"];

const chartsOptions = ref({
  grid: {
    left: '0',
    right: '0',
    // bottom: '10%',
    // top: '3%'
    containLabel: true,
  },
  legend: {
    show: true,
    itemWidth: 12,
    itemHeight: 12,
    bottom: "5%",
  },
  xAxis: {
    type: "category",
    data: [
      t("views.nfts.details.collection.jul_14"),
      t("views.nfts.details.collection.jul_26"),
      t("views.nfts.details.collection.aug_7"),
      t("views.nfts.details.collection.aug_12"),
      t("views.nfts.details.collection.aug_13"),
      t("views.nfts.details.collection.aug_13"),
      t("views.nfts.details.collection.aug_13"),
      t("views.nfts.details.collection.aug_12"),
      t("views.nfts.details.collection.aug_13"),
      t("views.nfts.details.collection.aug_13"),
      t("views.nfts.details.collection.aug_13"),
    ],
    axisTick: {
      show: false,
    },
    axisLabel: {
      textStyle: {
        color: "#8C8C8C",
      },
    },
    axisLine: {
      lineStyle: {
        color: "#8C8C8C",
      },
    },
  },
  yAxis: {
    type: "value",
    splitLine: {
      show: false,
    },
    axisLabel: {
      textStyle: {
        color: "#8C8C8C",
      },
    },
    axisLine: {
      lineStyle: {
        color: "#8C8C8C",
      },
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "line",
      z: 0, // 层级（权重）
      triggerOn: "click",
      lineStyle: {
        type: "solid", // 将虚线改为实线
        // width: 80, // 设置背景的宽度
        cap: "square",
        opacity: 1,
        color: "white", // 设置背景颜色为白色
        shadowBlur: 10, // 阴影模糊度
        shadowColor: "rgba(0, 0, 0, 0.1)", // 阴影颜色
        shadowOffsetX: 0, // 水平方向阴影偏移距离
        shadowOffsetY: 5,
      },
    },
  },
  series: [
    {
      name: t('views.nfts.details.collection.ethereum'),
      data: chartsData1.value,
      type: "line",
      smooth: true,
      symbolSize: 6,
      lineStyle: {
        width: 1,
        color: colors[0],
      },
    },
    {
      name: t('views.nfts.details.collection.abstract'),
      data: chartsData2.value,
      type: "line",
      smooth: true,
      symbolSize: 6,
      lineStyle: {
        width: 1,
        color: colors[1],
      },
    },
    {
      name: t('views.nfts.details.collection.apeChain'),
      data: chartsData3.value,
      type: "line",
      smooth: true,
      symbolSize: 6,
      lineStyle: {
        width: 1,
        color: colors[2],
      },
    },
    // {
    //     name: 'Abstract',
    //     data: chartsData2.value,
    //     type: 'line',
    //     smooth: true,
    //     symbolSize: 10,
    //     symbol: 'circle',
    //     lineStyle: {
    //         width: 3,
    //         color: '#7AD2DE'
    //     },
    //     itemStyle: {
    //         // 设置数据点（圆点）的颜色
    //         color: '#7AD2DE', // 可以是具体的颜色值，如 'blue'、'#123456' 等
    //         borderColor: '#fff', // 数据点边框颜色
    //         borderWidth: 2 // 数据点边框宽度
    //     }
    // },
    // {
    //     name: 'ApeChain',
    //     data: chartsData3.value,
    //     type: 'line',
    //     smooth: true,
    //     symbolSize: 10,
    //     symbol: 'circle',
    //     lineStyle: {
    //         width: 3,
    //         color: '#4080FF'
    //     },
    //     itemStyle: {
    //         color: '#4080FF',
    //         borderColor: '#fff',
    //         borderWidth: 2
    //     }
    // },
  ],
});
</script>
<style lang="scss" scoped></style>
