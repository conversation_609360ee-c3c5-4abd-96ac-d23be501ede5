<!-- AnalysisInfo -->
<template>
    <a-collapse class="cus-collapse" v-model:active-key="activeKeys" expand-icon-position="right" :bordered="false"
        :show-expand-icon="false">
        <a-collapse-item :header="$t('views.nfts.details.collection.trait')" key="0">
            <template #extra>
                <div class="flex items-center gap-[8px]">
                    5
                    <BaseIcon :icon="activeKeys.includes('0') ? 'down' : 'right'" />
                </div>
            </template>
            <Trait />
        </a-collapse-item>
        <a-collapse-item :header="$t('views.nfts.details.collection.priceHistory')" key="1">
            <template #extra>
                <BaseIcon :icon="activeKeys.includes('1') ? 'down' : 'right'" />
            </template>
            <PriceHistory />
        </a-collapse-item>
        <a-collapse-item :header="$t('views.nfts.details.collection.about1')" key="2">
            <template #extra>
                <BaseIcon :icon="activeKeys.includes('2') ? 'down' : 'right'" />
            </template>
            <CollectionAbout v-if="props.pageType === 'collection'" />
            <About v-else/>
        </a-collapse-item>
    </a-collapse>
</template>

<script setup>
import { ref } from "vue";
import BaseIcon from "@/components/BaseIcon/index.vue";
import Trait from '@/views/NFTS/Details/components/AnalysisInfo/Trait.vue'
import PriceHistory from '@/views/NFTS/Details/components/AnalysisInfo/PriceHistory.vue'
import About from '@/views/NFTS/Details/components/AnalysisInfo/About.vue'
import CollectionAbout from '@/views/NFTS/Details/components/AnalysisInfo/CollectionAbout.vue'

const props = defineProps({
    pageType: String
})

const activeKeys = ref(['0'])

</script>
<style lang="scss" scoped></style>
<style lang="scss">
.cus-collapse.arco-collapse {
    .arco-collapse-item-header {
        border-bottom-width: 0px;
    }

    .arco-collapse-item {
        border-bottom-width: 0px;

        .arco-collapse-item-content {
            background-color: #fff;

            .arco-collapse-item-content-box {
                padding-bottom: 12px;
                padding-top: 0px;
            }
        }

        .arco-collapse-item-header-title {
            color: var(#000);
            font-size: 24px;
            font-style: normal;
            font-weight: 700;
            line-height: 110%;
            padding: 12px 0px;
        }
    }
}
</style>