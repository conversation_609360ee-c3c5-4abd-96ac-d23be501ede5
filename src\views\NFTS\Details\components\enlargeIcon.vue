<template>
  <div
    class="cursor-pointer bg-[rgba(255,255,255,0.5)] hover:bg-[#FFFFFF] flex items-center justify-center absolute top-[32px] right-[32px]"
    :class="className"
    @click="$emit('click')"
  >
    <BaseIcon :class="iconClass" icon="shrink" color="#1A1A1A" />
  </div>
</template>
<script setup>
import { defineEmits } from "vue";
import BaseIcon from "@/components/BaseIcon/index.vue";
const props = defineProps({
  className: {
    type: String,
    default: "w-[40px] h-[40px] rounded-[7px]",
  },
  iconClass: {
    type: String,
    default: "text-[14px]",
  },
});
defineEmits("click");
</script>
<style lang="scss" scoped></style>
