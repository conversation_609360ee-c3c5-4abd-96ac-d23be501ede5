<template>
  <div
    class="show-info-tag cursor-pointer flex items-center px-[12px] h-[24px] text-[12px] bg-[#F2F2F2]"
    :class="className"
    @click="(showInfo = !showInfo), emit('update:modelValue', showInfo)"
  >
    <span class="mr-[8px]">{{ $t('views.nfts.details.collection.info') }}</span>
    <BaseIcon
      :icon="!showInfo ? 'caret-down' : 'caret-up'"
      class="text-[14px]"
      color="#1A1A1A"
    />
  </div>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue";

const props = defineProps({
  className: {
    type: String,
  },
  info: {
    type: Object,
    default: () => ({}),
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
});
const showInfo = ref(props.showMore);
const emit = defineEmits(["update:modelValue"]);
</script>
