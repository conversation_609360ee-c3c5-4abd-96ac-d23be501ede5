<template>
  <a-button
    class="info-outline-button flex items-center"
    type="outline"
    status="info"
    shape="round"
    @click="analysisClick"
  >
    <img
      class="w-[16px] mr-[4px] flex-shrink-0"
      src="@/assets/NFTS/details/LineChart.svg"
      alt=""
    />
    <!-- <BaseIcon icon="share-alt" class="text-[16px] mr-[4px] flex-shrink-0" /> -->
    {{ $t("views.nfts.details.collection.analysis") }}
  </a-button>
</template>
<script setup>
import bus from '@/utils/eventBus'

const analysisClick = () => {
  bus.emit('NFTSDetailsIsAnalysis')
}
</script>
<style lang="scss" scoped></style>
