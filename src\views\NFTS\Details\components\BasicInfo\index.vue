<!-- BasicInfo -->
<template>
  <div class="basic-info-container">
    <InfoTop
      :showNameIconGroups="showNameIconGroups"
      :showSubInfo="showSubInfo"
    />
    <CoinTabs class="mt-[40px]" />
    <OrderAndAnatherTable className="mt-[16px]" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import InfoTop from "@/views/NFTS/Details/components/BasicInfo/InfoTop/index.vue";
import OrderAndAnatherTable from "@/views/NFTS/Details/components/BasicInfo/OrderAndAnatherTable/index.vue";
import CoinTabs from "@/views/NFTS/Details/components/BasicInfo/coinTabs.vue";

const props = defineProps({
  showNameIconGroups: {
    type: Boolean,
    default: true,
  },
  showSubInfo: {
    type: Boolean,
    default: true,
  },
});
</script>
<style lang="scss" scoped></style>
