<template>
  <a-modal
    class="cus-modal"
    v-model:visible="visible"
    hide-title
    width="auto"
    :footer="false"
    @ok="visible = false"
    @cancel="visible = false"
  >
    <div class="dialog-header">
      <ImageCarousel
        :images="demoImages"
        :item-width="40"
        :gap="8"
        :scroll-step="2"
        @select="handleImageSelect"
      />
      <BaseIcon
        class="close-icon"
        icon="close"
        color="#000"
        @click="visible = false"
      />
    </div>
    <div class="content-box">
      <IndependentDetails :curImgUrl="selectedImage" v-if="!isAnalysisState" />
      <IndependentAnalysis :curImgUrl="selectedImage" v-else />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import BaseIcon from "@/components/BaseIcon/index.vue";
import IndependentDetails from "@/views/NFTS/Details/independent/index.vue";
import IndependentAnalysis from "@/views/NFTS/Details/independentAnalysis/index.vue";
import bus from "@/utils/eventBus";
import ImageCarousel from "@/components/ImageCarousel/index.vue";
import imgUrl_1 from "@/assets/NFTS/details/detail_1.png";
import imgUrl_2 from "@/assets/NFTS/details/detail_2.png";
import imgUrl_3 from "@/assets/NFTS/details/detail_3.png";
import imgUrl_4 from "@/assets/NFTS/details/detail_4.png";
import imgUrl_5 from "@/assets/NFTS/details/detail_5.png";
import imgUrl_0 from "@/assets/NFTS/details/detail_0.png";

const visible = defineModel();

const isAnalysisState = ref(false);

const changeType = () => {
  isAnalysisState.value = !isAnalysisState.value;
};

onMounted(() => {
  bus.on("NFTSDetailsIsAnalysis", changeType);
});

onBeforeUnmount(() => {
  bus.off("NFTSDetailsIsAnalysis", changeType);
});
// 示例图片数据
const demoImages = ref([
  {
    src: imgUrl_0,
  },
  {
    src: imgUrl_1,
  },
  {
    src: imgUrl_5,
  },
  {
    src: imgUrl_4,
  },
  {
    src: imgUrl_3,
  },
  {
    src: imgUrl_2,
  },
  {
    src: imgUrl_1,
  },
  {
    src: imgUrl_0,
  },
  {
    src: imgUrl_5,
  },
  {
    src: imgUrl_4,
  },
  {
    src: imgUrl_3,
  },
  {
    src: imgUrl_2,
  },
]);
const selectedImage = ref(demoImages.value[0].src);
// 事件处理
const handleImageSelect = (data) => {
  selectedImage.value = data.image.src;
};
</script>
<style lang="scss" scoped></style>
