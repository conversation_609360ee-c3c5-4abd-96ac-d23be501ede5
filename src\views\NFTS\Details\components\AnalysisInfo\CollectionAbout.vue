<template>
    <div class="about-box">
        <div class="header">{{$t('views.nfts.details.collection.about')}}</div>
        <div class="desc">
            {{ $t('views.nfts.details.collection.aboutDesc') }}
        </div>
        <div class="header">About Doodles</div>
        <div class="flex gap-[4px] text-[12px]">
            <span>{{$t('views.nfts.details.collection.by')}}</span>
            <img :src="certifyIcon" alt="logo" />
        </div>
        <div class="desc">
            {{ $t('views.nfts.details.collection.aboutDesc') }}
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import certifyIcon from '@/assets/NFTS/details/certify.svg'

</script>
<style lang="scss" scoped>
.about-box {
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 16px;
    background: var(--Neutral-50, #F9FAFB);

    .header {
        color: #000;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
    }

    .desc {
        color: #595959;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
    }
}
</style>