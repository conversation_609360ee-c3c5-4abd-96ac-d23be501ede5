import {
  createRouter,
  createWebHashHistory,
  createWebHistory,
  useRoute,
} from "vue-router";
import autoPage from "../utils/auto-route";

const router = createRouter({
  history: createWebHistory(), //createWebHashHistory会有# ，createWebHistory不会有 #
  routes: [
    {
      path: "/",
      name: "",
      component: () => import("@/components/Layout.vue"),
      redirect: "/home",
      children: [
        {
          path: "/home",
          name: "首页",
          component: () => import("@/views/home/<USER>"),
        },
        {
          path: "/tailwind-test",
          name: "Tailwind测试",
          component: () => import("@/views/TailwindTest.vue"),
        },
        {
          path: "/NFTSIndependentDetail",
          name: "NFTS独立详情",
          component: () => import("@/views/NFTS/Details/independent/index.vue"),
        },
        {
          path: "/NFTSCollectionDetail",
          name: "NFTS合集详情",
          component: () => import("@/views/NFTS/Details/collection/index.vue"),
        },
        {
          path: "/NFTSIndependentAnalysisDetail",
          name: "NFTS独立分析详情",
          component: () => import("@/views/NFTS/Details/independentAnalysis/index.vue"),
        },
        {
          path: "/NFTSCollectionAnalysisDetail",
          name: "NFTS合集分析详情",
          component: () => import("@/views/NFTS/Details/collectionAnalysis/index.vue"),
        },
        {
          path: "/NFTSDetailTest",
          name: "NFTS详情测试",
          component: () => import("@/views/NFTS/Details/testDetails/index.vue"),
        },
      ],
    },
  ],
});

export default router;
