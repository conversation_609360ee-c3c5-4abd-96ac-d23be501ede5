<template>
  <div :class="className">
    <a-button
      type="info"
      class="mr-[12px] h-[24px!important] px-[12px!important] rounded-[8px!important]"
      :class="buttonClass"
      >{{ $t("views.nfts.details.collection.batchOffer") }}</a-button
    >
    <a-button
      type="primary"
      class="h-[24px!important] px-[12px!important] rounded-[8px!important]"
      :class="buttonClass"
      >{{ $t("views.nfts.details.collection.Batch_Buy") }}</a-button
    >
  </div>
</template>
<script setup>
defineProps({
  className: {
    type: String,
    default: "flex items-center",
  },
  buttonClass: {
    type: String,
    default: "",
  },
});
</script>
