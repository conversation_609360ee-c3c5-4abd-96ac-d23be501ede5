<template>
  <a-table
    :columns="columns"
    :data="data"
    :pagination="false"
    :bordered="false"
  >
    <template #CHAIN="{ rowIndex }">
      <div class="flex gap-[4px] items-center">
        <img :src="data[rowIndex].CHAINIcon" />
        <div class="count-unit">{{ data[rowIndex].CHAIN }}</div>
      </div>
    </template>
    <template #PRICE="{ rowIndex }">
      <div class="floor-col">
        <div class="floor">{{ data[rowIndex].PRICE }}</div>
        <div class="floor-unit">
          {{ $t("views.nfts.details.collection.WETH") }}
        </div>
      </div>
    </template>
    <template #TOPOFFER="{ rowIndex }">
      <div class="floor-col">
        <div class="floor">{{ data[rowIndex].TOPOFFER }}</div>
        <div class="floor-unit">
          {{ $t("views.nfts.details.collection.WETH") }}
        </div>
      </div>
    </template>
    <template #RARITY="{ rowIndex }">
      <div class="floor-col">
        <div class="floor">#{{ data[rowIndex].RARITY }}</div>
      </div>
    </template>
    <template #LASTSALE="{ rowIndex }">
      <div class="floor-col">
        <div class="floor">{{ data[rowIndex].LASTSALE }}</div>
        <div class="floor-unit">
          {{ $t("views.nfts.details.collection.WETH") }}
        </div>
      </div>
    </template>
    <template #ACTION>
      <a-space class="w-[60px] table-opt-btn-box">
        <a-button>{{
          $t("views.nfts.details.collection.Make_offer")
        }}</a-button>
        <a-button type="primary">
          {{ $t("views.nfts.details.collection.Buy") }}</a-button
        >
      </a-space>
    </template>
  </a-table>
</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import ethereum from "@/assets/NFTS/details/ethereum_icon.svg";
import abstract from "@/assets/NFTS/details/abstract_icon.svg";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const columns = [
  {
    title: t("views.nfts.details.collection.CHAIN"),
    dataIndex: "CHAIN",
    slotName: "CHAIN",
  },
  {
    title: t("views.nfts.details.collection.PRICE"),
    dataIndex: "PRICE",
    slotName: "PRICE",
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: t("views.nfts.details.collection.TOP_OFFER"),
    dataIndex: "TOPOFFER",
    slotName: "TOPOFFER",
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: t("views.nfts.details.collection.RARITY"),
    dataIndex: "RARITY",
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: t("views.nfts.details.collection.lastSale"),
    dataIndex: "LASTSALE",
    slotName: "LASTSALE",
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: t("views.nfts.details.collection.ACTION"),
    dataIndex: "ACTION",
    slotName: "ACTION",
  },
];

const data = reactive([
  {
    CHAIN: t("views.nfts.details.collection.ethereum"),
    CHAINIcon: ethereum,
    PRICE: 10.82,
    TOPOFFER: "#1,023",
    RARITY: 10.82,
    LASTSALE: 10.82,
  },
  {
    CHAIN: t("views.nfts.details.collection.ethereum"),
    CHAINIcon: abstract,
    PRICE: 10.82,
    TOPOFFER: "#1,023",
    RARITY: 10.82,
    LASTSALE: 10.82,
  },
  {
    CHAIN: t("views.nfts.details.collection.ethereum"),
    CHAINIcon: ethereum,
    PRICE: 10.82,
    TOPOFFER: "#1,023",
    RARITY: 10.82,
    LASTSALE: 10.82,
  },
]);
</script>
<style lang="scss" scoped>
.floor-col {
  display: flex;
  align-items: center;
  gap: 4px;

  .floor {
    color: #000;
    font-size: 14px;
    font-weight: 700;
  }

  .floor-unit {
    color: #595959;
    font-size: 14px;
    font-weight: 400;
  }
}

.count-col {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-radius: 4px;
  background: #eff5ff;

  .count {
    color: #000;
    font-size: 14px;
    font-weight: 400;
  }

  .count-unit {
    color: #416eea;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
  }
}

.table-opt-btn-box {
  .arco-btn {
    border-radius: 8px;

    &.arco-btn-secondary {
      color: #000;
    }
  }
}
</style>
