<template>
  <a-card
    hoverable
    class="w-full common-card-border bg-[transparent!important] rounded-[16px!important] border-[#F2F2F2!important]"
    :class="className"
  >
    <a-radio-group v-model="activeTab" type="button">
      <a-radio v-for="item in tabsList" :key="item.value" :value="item.value">
        {{ item.label }}
      </a-radio>
    </a-radio-group>
    <OrderTable v-if="activeTab === 'Orders'" className="mt-[28px]" />
    <ActivityTable v-if="activeTab === 'Activity'" className="mt-[16px]" />
    <BlockchainDetails
      v-if="activeTab === 'Blockchain_details'"
      className="mt-[16px]"
    />
  </a-card>
</template>
<script setup>
import OrderTable from "@/views/NFTS/Details/components/BasicInfo/orderTable.vue";
import ActivityTable from "@/views/NFTS/Details/components/BasicInfo/activityTable.vue";
import BlockchainDetails from "@/views/NFTS/Details/components/BasicInfo/blockchainDetails.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
defineProps({
  className: String,
});
const tabsList = ref([
  {
    label: t("views.nfts.details.collection.Orders"),
    value: "Orders",
  },
  {
    label: t("views.nfts.details.collection.Activity"),
    value: "Activity",
  },
  {
    label: t("views.nfts.details.collection.Blockchain_details"),
    value: "Blockchain_details",
  },
]);
const activeTab = ref(tabsList.value?.[0]?.value);
</script>
