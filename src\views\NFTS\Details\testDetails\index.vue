<template>
  <a-space>
    <a-button @click="router.push('/NFTSIndependentDetail')"
      >NFTS{{ $t("views.nfts.details.collection.independent") }}</a-button
    >
    <a-button @click="router.push('/NFTSCollectionDetail')"
      >NFTS
      {{ $t("views.nfts.details.collection.Collection_Details") }}</a-button
    >
    <a-button @click="visible = true"
      >NFTS${{ $t("views.nfts.details.collection.detailDialog") }}</a-button
    >
  </a-space>
  <NFTSDetailsDialog v-model="visible" />
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import NFTSDetailsDialog from "@/views/NFTS/Details/Dialog/index.vue";

const router = useRouter();

const visible = ref(false);
</script>
<style lang="scss" scoped></style>
