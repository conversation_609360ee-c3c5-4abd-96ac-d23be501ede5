<template>
  <a-tabs class="coin-tabs" :class="className">
    <a-tab-pane v-for="item in tabs" :key="item.key">
      <template #title>
        <div class="flex items-center">
          <img class="mr-[4px]" :src="item.icon" alt="" />
          {{ item.key }}
        </div>
      </template>
      <a-card
        hoverable
        class="w-full common-card-border bg-[transparent!important] rounded-[16px!important] border-[#F2F2F2!important]"
      >
        <a-descriptions :data="data" layout="vertical" :column="4" />
        <a-descriptions
          class="summary-desc mt-[24px]"
          :data="data1"
          layout="inline-vertical"
          :column="4"
        >
        </a-descriptions>
        <a-button
          type="primary"
          class="block w-full font-[700!important] mt-[24px] h-[48px!important] rounded-[8px!important]"
          >{{ $t("views.nfts.details.collection.buy_now") }}</a-button
        >
        <a-button
          type="info"
          class="mr-[12px] font-[700!important] mt-[16px] block w-full h-[48px!important] rounded-[8px!important]"
          >{{ $t("views.nfts.details.collection.Make_offer") }}</a-button
        >
        <p class="text-center text-[#595959] mt-[16px]">
          {{ $t("views.nfts.details.collection.Sale_ends_in") }} 
        </p>
      </a-card>
    </a-tab-pane>
  </a-tabs>
</template>
<script setup>
import { h } from "vue";
import ethereum from "@/assets/NFTS/details/ethereum_icon.svg";
import abstract from "@/assets/NFTS/details/abstract_icon.svg";
import apeChain from "@/assets/NFTS/details/apeChain_icon.svg";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const tabs = ref([
  { key: t("views.nfts.details.collection.ethereum"), icon: ethereum },
  { key: t("views.nfts.details.collection.abstract"), icon: abstract },
  { key: t("views.nfts.details.collection.apeChain"), icon: apeChain },
]);
defineProps({
  className: String,
});
const data = ref([
  {
    label: t("views.nfts.details.collection.TOP_OFFER"),
    value: "0.45  ETH",
  },
  {
    label: t("views.nfts.details.collection.Collection_Floor"),
    value: "0.45  ETH",
  },
  {
    label: t("views.nfts.details.collection.RARITY"),
    value: "#9,962",
  },
  {
    label: t("views.nfts.details.collection.lastSale"),
    value: "0.45  ETH",
  },
  {
    label: t("views.nfts.details.collection.lastUpdate"),
    value: "0.45  ETH",
  },
]);
const data1 = ref([
  {
    label: t("views.nfts.details.collection.BUY_FOR"),
    value: h("div", {}, [
      h("p", { class: "text-[32px] font-700" }, "0.45  ETH"),
      h("p", { class: "text-[14px] text-[#595959]" }, "$4,483.23"),
    ]),
    span: 3,
  },
]);
</script>
