<template>
  <div class="w-full" :class="className">
    <div class="flex items-center gap-[24px] text-[14px]">
      <div class="flex items-center">
        <span>{{ $t('views.nfts.details.collection.by') }}</span>
        <BaseIcon class="ml-[5px]" icon="a-Multi-chainUnique" />
      </div>
      <span>2023.06.12</span>
      <span>{{ $t('views.nfts.details.collection.art') }}</span>
    </div>
    <p class="mt-[8px] text-justify">
      {{ $t('views.nfts.details.collection.aboutDesc1') }}
    </p>
  </div>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon/index.vue";

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  className: {
    type: String,
    default: "",
  },
});
</script>
