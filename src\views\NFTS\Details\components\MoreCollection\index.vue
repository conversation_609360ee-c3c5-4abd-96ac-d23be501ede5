<!-- More from this collection -->
<template>
    <div class="more-box">
        <div class="title">
            {{ $t('views.nfts.details.collection.more_from_this_collection') }}
        </div>

        <div class="items-box">
            <div class="item" v-for="item in collections">
                <div class="img-box">
                    <img :src="item.src" alt="">
                    <div class="top-left-icons">
                       <CoinIconsGap :coinList="item.topIcons"/>
                    </div>
                </div>
                <div class="bottom-box">
                    <div class="title-box">
                        <div class="title">{{ item.title }}</div>
                        <img :src="certifyIcon" alt="logo" />
                        <img :src="multiChain" alt="logo" />
                    </div>
                    <div class="value-box">{{ item.value }}</div>
                    <div class="last-sale-box">
                        <div class="label">{{ $t('views.nfts.details.collection.lastSale') }}</div>
                        <div class="value">{{ item.lastSale }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="view-collection-box">
            <div class="button ">{{ $t('views.nfts.details.collection.view_collection') }}</div>
        </div>
    </div>

</template>

<script setup>
import { ref, reactive, toRefs, watch } from "vue";
import detail_0 from '@/assets/NFTS/details/detail_0.png'
import detail_2 from '@/assets/NFTS/details/detail_2.png'
import detail_3 from '@/assets/NFTS/details/detail_3.png'
import detail_4 from '@/assets/NFTS/details/detail_4.png'
import detail_5 from '@/assets/NFTS/details/detail_5.png'
import certifyIcon from '@/assets/NFTS/details/certify.svg'
import multiChain from '@/assets/NFTS/details/multi_chain.svg'
import CoinIconsGap from "@/views/NFTS/Details/components/BasicInfo/coinIconsGap.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const collections = ref([
    {
        src: detail_0,
        topIcons: ["ethereum", "abstract"],
        title: t('views.nfts.details.collection.Bored Ape Yacht Club'),
        value: '0.45  ETH',
        lastSale: '1.139  ETH'
    },
    {
        src: detail_2,
        topIcons:  ["ethereum", "abstract","berachain"],
        title:  t('views.nfts.details.collection.Bored Ape Yacht Club'),
        value: '0.45  ETH',
        lastSale: '1.139  ETH'
    },
    {
        src: detail_3,
        topIcons: ["ethereum", "abstract","berachain","base"],
        title:  t('views.nfts.details.collection.Bored Ape Yacht Club'),
        value: '0.45  ETH',
        lastSale: '1.139  ETH'
    },
    {
        src: detail_4,
        topIcons: ["ethereum", "abstract", "berachain", "base", "more"],
        title:  t('views.nfts.details.collection.Bored Ape Yacht Club'),
        value: '0.45  ETH',
        lastSale: '1.139  ETH'
    },
    {
        src: detail_5,
        topIcons: ["ethereum", "base"],
        title:  t('views.nfts.details.collection.Bored Ape Yacht Club'),
        value: '0.45  ETH',
        lastSale: '1.139  ETH'
    }
])

</script>
<style lang="scss" scoped>
.more-box {
    .title {
        color: #000;
        text-align: center;
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 110%;
    }

    .items-box {
        margin-top: 32px;
        display: grid;
        grid-template-columns: repeat(5, 266px);
        gap: 12px;

        .item {
            border-radius: 16px;
            border: 1px solid #F4F4F4;
            transition: all .3s;
            &:hover {
                box-shadow: 0px 0px 12px #ccc;
            }


            .img-box {
                position: relative;

                img {
                    height: 264px;
                    width: 100%;
                    object-fit: cover;
                    border-radius: 15px 15px 0px 0px;
                }

                .top-left-icons {
                    position: absolute;
                    left: 11px;
                    top: 11px;
                }
            }

            .bottom-box {
                display: flex;
                padding: 12px;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                align-self: stretch;

                .title-box {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .title {
                        color: #000;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: 150%;
                    }
                }

                .value-box {
                    color: #000;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 140%;
                }

                .last-sale-box {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 140%;
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .label {
                        color: #8C8C8C;
                    }

                    .value {
                        color: #000;
                    }
                }

            }
        }
    }

    .view-collection-box {
        text-align: center;
        margin-top: 49px;
        margin-bottom: 78px;
        .button {
            display: inline-flex;
            height: 48px;
            padding: 13px 20px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
            border-radius: 8px;
            border: 1px solid #E6E6E6;
            color: #000;
            font-size: 14px;
            font-weight: 700;
            cursor: pointer;
            &:hover {
                background-color: #f4f4f4;
            }
        }
    }
}
</style>