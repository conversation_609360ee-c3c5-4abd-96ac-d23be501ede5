<template>
  <div :class="className">
    <div
      v-for="item in data"
      :key="item.key"
      class="flex items-center justify-between mb-[16px]"
    >
      <span class="text-[#595959]">{{ item.label }}</span>
      <span
        :class="
          ['Contract_Address', 'Token_ID'].includes(item.key)
            ? 'text-[#1B4BE5]'
            : 'text-[#000000]'
        "
        >{{ item.value }}</span
      >
    </div>
  </div>
</template>
<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
defineProps({
  className: String,
});
const data = ref([
  {
    key: "Contract_Address",
    label: t("views.nfts.details.collection.Contract_Address"),
    value: "0x1234...5678",
  },
  {
    key: "Token_ID",
    label: "Token ID",
    value: "#1234",
  },
  {
    key: "Token_Standard",
    label: t("views.nfts.details.collection.Token_Standard"),
    value: "ERC721",
  },
  {
    key: "Chain",
    label: t("views.nfts.details.collection.CHAIN"),
    value: "Ethereum",
  },
]);
</script>
